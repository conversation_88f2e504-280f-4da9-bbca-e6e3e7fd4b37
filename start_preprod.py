#!/usr/bin/env python3
"""
Pre-production startup script for OCR Document Grossiste API

This script starts the API server in pre-production mode with the appropriate configuration.

Usage:
    python start_preprod.py
    
Or directly with uvicorn:
    uvicorn src.api:app --host 0.0.0.0 --port 8090 --log-config=logging.yaml --env-file .env.preprod
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Start the API server in pre-production mode."""
    
    # Set environment variable to ensure preprod config is loaded
    os.environ['ENVIRONMENT'] = 'preprod'
    
    # Get the project root directory
    project_root = Path(__file__).parent
    
    # Change to project directory
    os.chdir(project_root)
    
    # Check if .env.preprod exists
    env_file = project_root / '.env.preprod'
    if not env_file.exists():
        print("❌ Error: .env.preprod file not found!")
        print("Please create .env.preprod with your pre-production configuration.")
        sys.exit(1)
    
    # Check if logging.yaml exists
    log_config = project_root / 'logging.yaml'
    if not log_config.exists():
        print("⚠️  Warning: logging.yaml not found, using default logging")
        log_config_arg = []
    else:
        log_config_arg = ['--log-config', 'logging.yaml']
    
    # Uvicorn command
    cmd = [
        'uvicorn',
        'src.api:app',
        '--host', '0.0.0.0',
        '--port', '8090',  # Pre-production port
        '--env-file', '.env.preprod'
    ] + log_config_arg
    
    print("🚀 Starting OCR Document Grossiste API in PRE-PRODUCTION mode...")
    print(f"📁 Working directory: {project_root}")
    print(f"🌐 Server will be available at: http://0.0.0.0:8090")
    print(f"📋 Environment file: .env.preprod")
    print(f"🔧 Command: {' '.join(cmd)}")
    print("-" * 60)
    
    try:
        # Start the server
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        sys.exit(0)

if __name__ == '__main__':
    main()
