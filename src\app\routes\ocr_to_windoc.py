from fastapi import APIRouter, HTTPException, Depends, Body
from src.app.dependencies import check_windoc_headers
import logging
import json
from ..utils.models import RequestCriteria, PreBlOcrResponse
from ..utils.db_operations import get_bl_scannes_with_criteria, get_bl_scanne_by_id_with_criteria, update_bl_status_valider_windoc
from concurrent.futures import ThreadPoolExecutor
import asyncio
import traceback

router = APIRouter()

# Create a ThreadPoolExecutor for running blocking operations
executor = ThreadPoolExecutor(max_workers=4)

logger = logging.getLogger(__name__)


@router.post("/getListBLScannes")
async def get_list_bl_scannes(
    criteria: RequestCriteria = Body(...),
    src_app: str = "winpluspharma",
    auth_data: dict = Depends(check_windoc_headers)
):
    """
    Get list of BL scannes with dynamic filtering criteria.
    
    POST endpoint that accepts RequestCriteria in the body for flexible filtering:
    - NumeroBL: Filter by ID_BL (exact match)
    - dateBl: Filter by date_BL_origine (exact match)  
    - dateBlDebut: Start date for date range filtering
    - dateBlFin: End date for date range filtering
    
    Headers required:
    - Authorization: Static token
    - IdHashUser: User hash identifier
    """
    try:
        # Run the process in a ThreadPoolExecutor
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor,
            lambda: asyncio.run(get_list_bl_scannes_process(criteria, auth_data, src_app))
        )

        # Return the result
        return result

    except HTTPException as http_exc:
        logging.error(f"HTTP Error: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logging.error(f"Unexpected error: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/getBLScanneById")
async def get_bl_scanne_by_id(
    criteria: RequestCriteria = Body(...),
    src_app: str = "winpluspharma",
    auth_data: dict = Depends(check_windoc_headers)
):
    """
    Get a specific BL scanne by ID from criteria.

    POST endpoint that expects RequestCriteria in the body with BL ID information.
    The ID should be provided in the criteria (blId field).

    Headers required:
    - Authorization: Static token
    - IdHashUser: User hash identifier
    """
    try:
        # Run the process in a ThreadPoolExecutor
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor,
            lambda: asyncio.run(get_bl_scanne_by_id_process(criteria, auth_data, src_app))
        )

        # Return the result
        return result

    except HTTPException as http_exc:
        logging.error(f"HTTP Error: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logging.error(f"Unexpected error: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Internal server error")


async def get_list_bl_scannes_process(criteria: RequestCriteria, auth_data: dict, src_app: str = "winpluspharma"):
    """
    Process function for getting list of BL scannes with criteria filtering.
    """
    try:
        id_hash_user = auth_data.get("IdHashUser")
        
        logger.info(f"IdHashUser: {id_hash_user}")
        logger.info(f"Criteria: {criteria.dict(exclude_none=True)}")
        logger.info(f"Source App: {src_app}")

        if not id_hash_user:
            raise HTTPException(status_code=401, detail="Invalid IdHashUser")

        # Convert criteria to dict, excluding None values for dynamic filtering
        criteria_dict = criteria.dict(exclude_none=True)
        
        # Get filtered results
        results = get_bl_scannes_with_criteria(criteria_dict, id_hash_user, src_app)
        
        logger.info(f"Found {len(results)} records matching criteria")
        return results
        
    except Exception as e:
        logger.error(f"Error in get_list_bl_scannes_process: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


async def get_bl_scanne_by_id_process(criteria: RequestCriteria, auth_data: dict, src_app: str = "winpluspharma"):
    """
    Process function for getting a specific BL scanne by ID from criteria.
    """
    try:
        id_hash_user = auth_data.get("IdHashUser")

        logger.info(f"IdHashUser: {id_hash_user}")
        logger.info(f"Criteria: {criteria.dict(exclude_none=True)}")
        logger.info(f"Source App: {src_app}")

        if not id_hash_user:
            raise HTTPException(status_code=401, detail="Invalid IdHashUser")

        # Extract BL ID from criteria
        bl_id = None
        if criteria.blId:
            try:
                bl_id = int(criteria.blId)
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid blId format - must be a number")
        else:
            raise HTTPException(status_code=400, detail="blId is required in criteria")

        # Get specific record by ID
        result = get_bl_scanne_by_id_with_criteria(bl_id, id_hash_user, src_app)

        if result is None:
            raise HTTPException(status_code=404, detail=f"BL Scanne with ID {bl_id} not found")

        return PreBlOcrResponse(**result)

    except Exception as e:
        logger.error(f"Error in get_bl_scanne_by_id_process: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/updateBlStatusValider")
async def update_bl_status_valider_windoc_endpoint(
    criteria: RequestCriteria = Body(...),
    auth_data: dict = Depends(check_windoc_headers)
):
    """
    Update BL status to VALIDER using criteria.

    POST endpoint that expects RequestCriteria in the body with BL ID information.
    The blId field is required in the criteria to identify which BL to update.

    Headers required:
    - Authorization: Static token
    - IdHashUser: User hash identifier

    Returns:
    - 1 if update successful, 0 if failed
    """
    try:
        # Run the process in a ThreadPoolExecutor
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor,
            lambda: asyncio.run(update_bl_status_valider_windoc_process(criteria, auth_data))
        )

        # Return the result
        return result

    except HTTPException as http_exc:
        logging.error(f"HTTP Error: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logging.error(f"Unexpected error: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Internal server error")


async def update_bl_status_valider_windoc_process(criteria: RequestCriteria, auth_data: dict):
    """
    Process function for updating BL status to VALIDER using criteria.
    """
    try:
        id_hash_user = auth_data.get("IdHashUser")

        logger.info(f"IdHashUser: {id_hash_user}")
        logger.info(f"Criteria: {criteria.dict(exclude_none=True)}")

        if not id_hash_user:
            raise HTTPException(status_code=401, detail="Invalid IdHashUser")

        # Validate that blId is provided
        if not criteria.blId:
            raise HTTPException(status_code=400, detail="blId is required in criteria to update BL status")

        # Convert criteria to dict, excluding None values
        criteria_dict = criteria.dict(exclude_none=True)

        # Update BL status to VALIDER
        result = update_bl_status_valider_windoc(criteria_dict, id_hash_user)

        logger.info(f"Update result for BL ID {criteria.blId}: {result}")
        return result  # Return 1 for success, 0 for failure

    except Exception as e:
        logger.error(f"Error in update_bl_status_valider_windoc_process: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
