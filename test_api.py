#!/usr/bin/env python3
"""
Test the getBLScanneById API endpoint
"""

import requests
import json

def test_api():
    url = "http://localhost:8088/windoc/getBLScanneById"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "kcS5RMvQ4JRuVcqEwyFdbJbpbPOk5ZSBAm4CsrqeX106f0Pi8Lt4qBL0Uitk2TtqPJtmqFwWa1iX5abIjwj3ppHeXymxgkWr0Gy6jIcIFQmYWZxywfQqllvV",
        "IdHashUser": "CLT3569"
    }
    
    data = {
        "codeSite": 172,
        "blId": "42"
    }
    
    try:
        print(f"🔍 Testing API endpoint: {url}")
        print(f"📤 Request data: {json.dumps(data, indent=2)}")
        print(f"📋 Headers: IdHashUser = {headers['IdHashUser']}")
        
        response = requests.post(url, headers=headers, json=data)
        
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📄 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success! Response:")
            print(json.dumps(result, indent=2))
        else:
            print(f"❌ Error Response:")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    test_api()
