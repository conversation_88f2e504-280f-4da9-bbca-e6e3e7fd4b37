#!/usr/bin/env python3
"""
Debug script to check what BL records exist for a specific user
"""

import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine, text

def check_user_data(user_id="CLT3569", env_file=".env.preprod"):
    """Check what BL records exist for a specific user."""
    try:
        # Load the specified environment
        load_dotenv(project_root / env_file)
        from src.app.config import DATABASE_URL

        print(f"🔍 Checking {env_file} environment")
        print(f"📊 Database: {DATABASE_URL.split('@')[1] if '@' in DATABASE_URL else 'local'}")
        print()

        engine = create_engine(DATABASE_URL)
        
        with engine.connect() as conn:
            # Get all records for this user
            result = conn.execute(text("""
                SELECT
                    "ID_BL",
                    "id_BL_origine",
                    "status",
                    "ID_USER",
                    "supplier_name",
                    "date",
                    "src_app"
                FROM pre_bl_ocr
                WHERE "ID_USER" = :user_id
                ORDER BY "date" DESC
                LIMIT 10
            """), {"user_id": user_id})
            
            records = result.fetchall()
            
            print(f"🔍 Records for user {user_id}:")
            print(f"📊 Found {len(records)} records")
            print()
            
            if records:
                print("📋 Recent records:")
                print("ID_BL | id_BL_origine | status     | supplier_name | date                | src_app")
                print("-" * 90)
                
                for record in records:
                    id_bl, id_bl_origine, status, id_user, supplier_name, date, src_app = record
                    date_str = str(date)[:19] if date else "None"
                    supplier_str = (supplier_name or "None")[:15]
                    src_app_str = src_app or "None"
                    
                    print(f"{id_bl:5} | {id_bl_origine or 'None':13} | {status:10} | {supplier_str:13} | {date_str} | {src_app_str}")
            
            # Check specifically for records with status EN_COURS
            result = conn.execute(text("""
                SELECT COUNT(*)
                FROM pre_bl_ocr
                WHERE "ID_USER" = :user_id AND "status" = 'EN_COURS'
            """), {"user_id": user_id})
            
            en_cours_count = result.fetchone()[0]
            print(f"\n📊 Records with status 'EN_COURS': {en_cours_count}")
            
            # Check for record with id_BL_origine = '42'
            result = conn.execute(text("""
                SELECT
                    "ID_BL",
                    "id_BL_origine",
                    "status",
                    "supplier_name"
                FROM pre_bl_ocr
                WHERE "ID_USER" = :user_id AND "id_BL_origine" = '42'
            """), {"user_id": user_id})
            
            record_42 = result.fetchone()
            if record_42:
                id_bl, id_bl_origine, status, supplier_name = record_42
                print(f"\n🎯 Found record with id_BL_origine='42':")
                print(f"   ID_BL: {id_bl}")
                print(f"   id_BL_origine: {id_bl_origine}")
                print(f"   status: {status}")
                print(f"   supplier_name: {supplier_name}")
            else:
                print(f"\n❌ No record found with id_BL_origine='42' for user {user_id}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        env_file = sys.argv[1]
        print(f"Using environment: {env_file}")
        check_user_data(env_file=env_file)
    else:
        print("Checking preprod environment:")
        check_user_data(env_file=".env.preprod")

        print("\n" + "="*80 + "\n")

        print("Checking production environment:")
        check_user_data(env_file=".env.prod")
