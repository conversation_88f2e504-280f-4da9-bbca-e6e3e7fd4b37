#!/usr/bin/env python3
"""
Check what users and data exist in the database
"""

import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load preprod environment
load_dotenv(project_root / '.env.preprod')

from sqlalchemy import create_engine, text
from src.app.config import DATABASE_URL

def check_all_data():
    """Check what data exists in the database."""
    try:
        engine = create_engine(DATABASE_URL)
        
        with engine.connect() as conn:
            # Check if table exists
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'pre_bl_ocr'
                );
            """))
            
            table_exists = result.fetchone()[0]
            
            if not table_exists:
                print("❌ Table 'pre_bl_ocr' does not exist!")
                return
            
            print("✅ Table 'pre_bl_ocr' exists")
            
            # Get total record count
            result = conn.execute(text('SELECT COUNT(*) FROM pre_bl_ocr'))
            total_count = result.fetchone()[0]
            print(f"📊 Total records: {total_count}")
            
            if total_count == 0:
                print("❌ Database is empty!")
                return
            
            # Get unique users
            result = conn.execute(text("""
                SELECT "ID_USER", COUNT(*) as record_count
                FROM pre_bl_ocr 
                GROUP BY "ID_USER"
                ORDER BY record_count DESC
                LIMIT 10
            """))
            
            users = result.fetchall()
            print(f"\n👥 Users in database:")
            print("User ID      | Record Count")
            print("-" * 30)
            
            for user_id, count in users:
                user_str = (user_id or "NULL")[:12]
                print(f"{user_str:12} | {count}")
            
            # Get status distribution
            result = conn.execute(text("""
                SELECT "status", COUNT(*) as count
                FROM pre_bl_ocr 
                GROUP BY "status"
                ORDER BY count DESC
            """))
            
            statuses = result.fetchall()
            print(f"\n📊 Status distribution:")
            print("Status       | Count")
            print("-" * 20)
            
            for status, count in statuses:
                status_str = (status or "NULL")[:12]
                print(f"{status_str:12} | {count}")
            
            # Get recent records
            result = conn.execute(text("""
                SELECT 
                    "ID_BL",
                    "id_BL_origine",
                    "ID_USER",
                    "status",
                    "date"
                FROM pre_bl_ocr 
                ORDER BY "date" DESC
                LIMIT 5
            """))
            
            recent = result.fetchall()
            print(f"\n📋 Recent records:")
            print("ID_BL | id_BL_origine | ID_USER  | status     | date")
            print("-" * 60)
            
            for record in recent:
                id_bl, id_bl_origine, id_user, status, date = record
                date_str = str(date)[:19] if date else "None"
                user_str = (id_user or "None")[:8]
                origine_str = (id_bl_origine or "None")[:13]
                status_str = (status or "None")[:10]
                
                print(f"{id_bl:5} | {origine_str:13} | {user_str:8} | {status_str:10} | {date_str}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_all_data()
