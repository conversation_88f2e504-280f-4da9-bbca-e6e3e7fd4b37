@echo off
REM Pre-production startup script for OCR Document Grossiste API (Windows)
REM This script starts the API server in pre-production mode

echo.
echo ========================================
echo  OCR Document Grossiste API - PREPROD
echo ========================================
echo.

REM Set environment variable
set ENVIRONMENT=preprod

REM Check if .env.preprod exists
if not exist ".env.preprod" (
    echo ❌ Error: .env.preprod file not found!
    echo Please create .env.preprod with your pre-production configuration.
    pause
    exit /b 1
)

echo 🚀 Starting OCR Document Grossiste API in PRE-PRODUCTION mode...
echo 📁 Working directory: %CD%
echo 🌐 Server will be available at: http://0.0.0.0:8090
echo 📋 Environment file: .env.preprod
echo.

REM Start the server
uvicorn src.api:app --host 0.0.0.0 --port 8090 --log-config=logging.yaml --env-file .env.preprod

echo.
echo 🛑 Server stopped
pause
