# Environment Setup Guide

This document explains how to set up and run the OCR Document Grossiste API in different environments.

## 🌍 **Available Environments**

### 1. **Local Development** (`local`)
- **Port**: 8088
- **Environment File**: `.env.local`
- **Purpose**: Development and testing
- **Debug**: Enabled

### 2. **Pre-Production** (`preprod`)
- **Port**: 8090
- **Environment File**: `.env.preprod`
- **Purpose**: Testing before production deployment
- **Debug**: Disabled

### 3. **Production** (`prod`)
- **Port**: 8089
- **Environment File**: `.env.prod`
- **Purpose**: Live production environment
- **Debug**: Disabled

## 🚀 **Running the API**

### **Method 1: Using Uvicorn directly**

```bash
# Local Development
uvicorn src.api:app --host 0.0.0.0 --port 8088 --log-config=logging.yaml --env-file .env.local

# Pre-Production
uvicorn src.api:app --host 0.0.0.0 --port 8090 --log-config=logging.yaml --env-file .env.preprod

# Production
uvicorn src.api:app --host 0.0.0.0 --port 8089 --log-config=logging.yaml --env-file .env.prod
```

### **Method 2: Using startup scripts**

```bash
# Pre-Production (using the startup script)
python start_preprod.py

# Or for other environments, create similar scripts
```

## 📋 **Environment Configuration**

### **Required Environment Variables**

Each environment file (`.env.local`, `.env.preprod`, `.env.prod`) must contain:

```bash
# Application Environment
ENVIRONMENT=local|preprod|prod
DEBUG=True|False
SYS_ARGV=api

# Database Configuration
POSTGRES_HOST=your-postgres-host
POSTGRES_PORT=5432
POSTGRES_DB=your-database-name
POSTGRES_USER=your-username
POSTGRES_PASSWORD=your-password
POSTGRES_SSL_MODE=prefer|require|disable

# API URLs
API_URL=https://your-api-url
TAP_URL=https://your-tap-url

# Authentication URLs
WINPLUS_AUTH_USER=https://your-winplus-auth-url
WINPLUS_AUTH_TENANT=https://your-winplus-tenant-url
WINPLUS_URL=https://your-winplus-url
PHARMALIEN_AUTH_URL=https://your-pharmalien-auth-url

# JWT Configuration
SECRET_KEY=your-secret-key
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=180

# OCR Configuration
TESSERACT_PATH=/path/to/tesseract
```

## 🔧 **Database Migration for New Environment**

When setting up a new environment, run the database migration:

```bash
# For preprod environment
python scripts/add_src_app_column.py --env-file .env.preprod --dry-run
python scripts/add_src_app_column.py --env-file .env.preprod

# Test the migration
python scripts/test_src_app_migration.py --env-file .env.preprod
```

## 🌐 **Port Allocation**

- **8088**: Local development
- **8089**: Production
- **8090**: Pre-production

## 📝 **Environment-Specific Notes**

### **Local Development**
- Uses local database or development database
- Debug mode enabled
- Detailed logging
- Hot reload enabled

### **Pre-Production**
- Mirrors production configuration
- Uses pre-production database
- Debug mode disabled
- Production-like logging
- Used for final testing before production deployment

### **Production**
- Live production database
- Debug mode disabled
- Optimized logging
- SSL/TLS enabled
- Performance monitoring enabled

## 🔍 **Troubleshooting**

### **Environment Not Loading**
1. Check that the `ENVIRONMENT` variable is set correctly
2. Verify the environment file exists (`.env.local`, `.env.preprod`, `.env.prod`)
3. Ensure all required variables are defined in the environment file

### **Database Connection Issues**
1. Verify database credentials in the environment file
2. Check network connectivity to the database host
3. Run the database health check:
   ```bash
   python -c "from src.app.database.connection import db_manager; print(db_manager.health_check())"
   ```

### **Port Already in Use**
If you get a "port already in use" error, either:
1. Stop the existing process using that port
2. Change the port number in the uvicorn command
3. Use `netstat -ano | findstr :8090` (Windows) or `lsof -i :8090` (Linux/Mac) to find the process using the port
