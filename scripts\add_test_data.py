#!/usr/bin/env python3
"""
Add test data for user CLT3569
"""

import sys
import os
from pathlib import Path
from dotenv import load_dotenv
import json
from datetime import datetime

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load preprod environment
load_dotenv(project_root / '.env.preprod')

from sqlalchemy import create_engine, text
from src.app.config import DATABASE_URL

def add_test_data():
    """Add test data for user CLT3569."""
    try:
        engine = create_engine(DATABASE_URL)
        
        # Sample OCR content
        sample_content = {
            "supplier_name": "Test Supplier",
            "bl_number": "42",
            "date": "2025-07-02",
            "items": [
                {"name": "Product 1", "quantity": 10, "price": 25.50},
                {"name": "Product 2", "quantity": 5, "price": 15.00}
            ],
            "total": 202.50
        }
        
        test_records = [
            {
                "id_bl_origine": "42",
                "id_user": "CLT3569",
                "id_tenant": "TENANT001",
                "code_tenant": "T001",
                "status": "EN_COURS",
                "supplier_name": "Test Supplier A",
                "supplier_id": "SUP001",
                "date_bl_origine": "2025-07-02",
                "random_id": "test_42_001",
                "src_app": "winpluspharma",
                "content": json.dumps(sample_content)
            },
            {
                "id_bl_origine": "43",
                "id_user": "CLT3569",
                "id_tenant": "TENANT001",
                "code_tenant": "T001",
                "status": "EN_COURS",
                "supplier_name": "Test Supplier B",
                "supplier_id": "SUP002",
                "date_bl_origine": "2025-07-01",
                "random_id": "test_43_001",
                "src_app": "winpluspharma",
                "content": json.dumps({**sample_content, "bl_number": "43"})
            },
            {
                "id_bl_origine": "44",
                "id_user": "CLT3569",
                "id_tenant": "TENANT001",
                "code_tenant": "T001",
                "status": "VALIDER",
                "supplier_name": "Test Supplier C",
                "supplier_id": "SUP003",
                "date_bl_origine": "2025-06-30",
                "random_id": "test_44_001",
                "src_app": "winpluspharma",
                "content": json.dumps({**sample_content, "bl_number": "44"})
            }
        ]
        
        with engine.connect() as conn:
            for record in test_records:
                result = conn.execute(text("""
                    INSERT INTO pre_bl_ocr (
                        "id_BL_origine",
                        "ID_USER",
                        "ID_TENANT",
                        "CODE_TENANT",
                        "status",
                        "supplier_name",
                        "supplier_id",
                        "date_BL_origine",
                        "random_id",
                        "src_app",
                        "Content"
                    ) VALUES (
                        :id_bl_origine,
                        :id_user,
                        :id_tenant,
                        :code_tenant,
                        :status,
                        :supplier_name,
                        :supplier_id,
                        :date_bl_origine,
                        :random_id,
                        :src_app,
                        :content
                    ) RETURNING "ID_BL"
                """), record)
                
                new_id = result.fetchone()[0]
                print(f"✅ Added record ID_BL={new_id}, id_BL_origine='{record['id_bl_origine']}', status='{record['status']}'")
            
            # Commit the transaction
            conn.commit()
            
        print(f"\n🎉 Successfully added {len(test_records)} test records for user CLT3569")
        print("\nNow you can test with:")
        print("- IdHashUser: CLT3569")
        print("- blId: 42 (status: EN_COURS)")
        print("- blId: 43 (status: EN_COURS)")
        print("- blId: 44 (status: VALIDER)")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    add_test_data()
